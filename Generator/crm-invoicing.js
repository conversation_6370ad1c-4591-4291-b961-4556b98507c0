// CRM and Invoicing System
// Data structures and functionality for managing clients, invoices, and services

// Company data (predefined)
const COMPANY_DATA = {
    name: "Anima mundi spol. s r.o.",
    director: "<PERSON><PERSON><PERSON><PERSON>",
    address: "Zimná 316",
    city: "07602 Novosad",
    ico: "********",
    dic: "**********",
    icDph: "SK**********",
    bankAccount: "SK09 0900 0000 0051 4221 3602",
    phone: "+421 xxx xxx xxx", // configurable
    email: "<EMAIL>", // configurable
    website: "www.animamundi.sk" // configurable
};

// Service definitions with prices (as per prompt)
const SERVICES = {
    // Basic services
    "zakladna_male_urnove": { 
        name: "<PERSON><PERSON><PERSON><PERSON> údržba - Malé urnové miesto", 
        price: 12, 
        category: "basic" 
    },
    "zakladna_velke_urnove": { 
        name: "<PERSON><PERSON><PERSON><PERSON> údržba - Veľké urnové miesto", 
        price: 17, 
        category: "basic" 
    },
    "zakladna_jednohrob": { 
        name: "<PERSON><PERSON><PERSON><PERSON> údržba - Jednohrob", 
        price: 29, 
        category: "basic" 
    },
    "zakladna_dvojhrob": { 
        name: "Základná údržba - Dvojhrob", 
        price: 40, 
        category: "basic" 
    },
    
    // Deep cleaning
    "hlbkove_male_urnove": { 
        name: "Hĺbkové čistenie - Malé urnové miesto", 
        price: 45, 
        category: "basic" 
    },
    "hlbkove_velke_urnove": { 
        name: "Hĺbkové čistenie - Veľké urnové miesto", 
        price: 58, 
        category: "basic" 
    },
    "hlbkove_jednohrob": { 
        name: "Hĺbkové čistenie - Jednohrob", 
        price: 115, 
        category: "basic" 
    },
    "hlbkove_dvojhrob": { 
        name: "Hĺbkové čistenie - Dvojhrob", 
        price: 161, 
        category: "basic" 
    },
    
    // Holiday packages
    "sviatocny_male_urnove": { 
        name: "Balík Sviatočný - Malé urnové miesto", 
        price: 45, 
        period: "rok", 
        category: "package" 
    },
    "sviatocny_velke_urnove": { 
        name: "Balík Sviatočný - Veľké urnové miesto", 
        price: 63, 
        period: "rok", 
        category: "package" 
    },
    "sviatocny_jednohrob": { 
        name: "Balík Sviatočný - Jednohrob", 
        price: 104, 
        period: "rok", 
        category: "package" 
    },
    "sviatocny_dvojhrob": { 
        name: "Balík Sviatočný - Dvojhrob", 
        price: 150, 
        period: "rok", 
        category: "package" 
    },
    
    // Digital services
    "qr_zakladny": { 
        name: "QR kód - Základný balíček", 
        price: 35, 
        period: "rok", 
        category: "digital" 
    },
    "qr_rozsireny": { 
        name: "QR kód - Rozšírený balíček (3 roky)", 
        price: 65, 
        period: "3 roky", 
        category: "digital" 
    },
    "qr_rodinny": { 
        name: "QR kód - Rodinný balíček (5 rokov)", 
        price: 95, 
        period: "5 rokov", 
        category: "digital" 
    }
};

// Global data storage
let clients = [];
let invoices = [];
let invoiceCounter = 1;

// Data structure definitions
class Client {
    constructor(data) {
        this.id = data.id || generateId('CLI');
        this.name = data.name;
        this.phone = data.phone;
        this.email = data.email;
        this.address = data.address || '';
        this.ico = data.ico || '';
        this.dic = data.dic || '';
        this.graves = data.graves || [];
        this.notes = data.notes || '';
        this.totalRevenue = data.totalRevenue || 0;
        this.activeContracts = data.activeContracts || 0;
        this.createdAt = data.createdAt || new Date().toISOString();
    }
}

class Invoice {
    constructor(data) {
        this.id = data.id || generateId('INV');
        this.number = data.number || generateInvoiceNumber();
        this.clientId = data.clientId;
        this.issueDate = data.issueDate || new Date().toISOString().split('T')[0];
        this.dueDate = data.dueDate || calculateDueDate(2); // 2 days default
        this.items = data.items || [];
        this.subtotal = data.subtotal || 0;
        this.vat = data.vat || 0;
        this.total = data.total || 0;
        this.status = data.status || 'pending'; // pending/paid/overdue
        this.paidDate = data.paidDate || null;
        this.notes = data.notes || '';
        this.variableSymbol = data.variableSymbol || this.number;
        this.orderId = data.orderId || null; // link to order if created from order
    }
}

class InvoiceItem {
    constructor(data) {
        this.serviceId = data.serviceId;
        this.name = data.name;
        this.price = data.price;
        this.quantity = data.quantity || 1;
        this.total = data.total || (data.price * (data.quantity || 1));
    }
}

// Utility functions
function generateId(prefix) {
    return prefix + '-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
}

function generateInvoiceNumber() {
    const year = new Date().getFullYear();
    const number = String(invoiceCounter).padStart(3, '0');
    invoiceCounter++;
    return `${year}${number}`;
}

function calculateDueDate(days) {
    const date = new Date();
    date.setDate(date.getDate() + days);
    return date.toISOString().split('T')[0];
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('sk-SK', {
        style: 'currency',
        currency: 'EUR',
        minimumFractionDigits: 2
    }).format(amount);
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('sk-SK');
}

// VAT calculations (20% VAT rate for Slovakia)
function calculateVAT(amount) {
    return amount * 0.2;
}

function calculateSubtotal(total) {
    return total / 1.2;
}

// Data persistence
function saveClientsData() {
    localStorage.setItem('crm_clients', JSON.stringify(clients));
}

function loadClientsData() {
    const data = localStorage.getItem('crm_clients');
    if (data) {
        clients = JSON.parse(data).map(clientData => new Client(clientData));
    }
}

function saveInvoicesData() {
    localStorage.setItem('crm_invoices', JSON.stringify(invoices));
    localStorage.setItem('crm_invoice_counter', invoiceCounter.toString());
}

function loadInvoicesData() {
    const data = localStorage.getItem('crm_invoices');
    if (data) {
        invoices = JSON.parse(data).map(invoiceData => new Invoice(invoiceData));
    }
    
    const counter = localStorage.getItem('crm_invoice_counter');
    if (counter) {
        invoiceCounter = parseInt(counter);
    }
}

// Initialize CRM system
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('clients-tab')) {
        initializeCRMSystem();
    }
});

function initializeCRMSystem() {
    loadClientsData();
    loadInvoicesData();

    // Initialize tab navigation for new tabs
    const navTabs = document.querySelectorAll('.nav-tab');
    navTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const tabName = this.dataset.tab;
            switchTab(tabName);
        });
    });

    // Initialize search and filters
    initializeClientFilters();
    initializeInvoiceFilters();

    // Update displays
    updateClientsDisplay();
    updateInvoicesDisplay();
    updateServicesDisplay();
    updateCRMStats();
}

// Client management functions
function updateClientsDisplay() {
    const clientsList = document.getElementById('clientsList');
    if (!clientsList) return;

    if (clients.length === 0) {
        clientsList.innerHTML = `
            <div class="no-clients">
                <i class="fas fa-users"></i>
                <h4>Žiadni klienti</h4>
                <p>Začnite pridaním nového klienta</p>
                <button class="btn btn-primary" onclick="showAddClientModal()">
                    <i class="fas fa-plus"></i> Pridať prvého klienta
                </button>
            </div>
        `;
        return;
    }

    const searchTerm = document.getElementById('clientSearch')?.value.toLowerCase() || '';
    const sortBy = document.getElementById('clientSort')?.value || 'name';

    let filteredClients = clients.filter(client =>
        client.name.toLowerCase().includes(searchTerm) ||
        client.phone.includes(searchTerm) ||
        client.email.toLowerCase().includes(searchTerm)
    );

    // Sort clients
    filteredClients.sort((a, b) => {
        switch(sortBy) {
            case 'revenue':
                return b.totalRevenue - a.totalRevenue;
            case 'created':
                return new Date(b.createdAt) - new Date(a.createdAt);
            default: // name
                return a.name.localeCompare(b.name);
        }
    });

    clientsList.innerHTML = filteredClients.map(client => `
        <div class="client-item" data-client-id="${client.id}">
            <div class="client-header">
                <div class="client-avatar">${client.name.charAt(0).toUpperCase()}</div>
                <div class="client-info">
                    <h4 class="client-name">${client.name}</h4>
                    <div class="client-contact">
                        <span><i class="fas fa-phone"></i> ${client.phone}</span>
                        <span><i class="fas fa-envelope"></i> ${client.email}</span>
                    </div>
                    ${client.address ? `<div class="client-address"><i class="fas fa-map-marker-alt"></i> ${client.address}</div>` : ''}
                </div>
                <div class="client-stats">
                    <div class="stat">
                        <span class="stat-value">${formatCurrency(client.totalRevenue)}</span>
                        <span class="stat-label">Tržby</span>
                    </div>
                    <div class="stat">
                        <span class="stat-value">${client.activeContracts}</span>
                        <span class="stat-label">Zmluvy</span>
                    </div>
                </div>
            </div>
            <div class="client-actions">
                <button class="btn btn-secondary btn-sm" onclick="editClient('${client.id}')">
                    <i class="fas fa-edit"></i> Upraviť
                </button>
                <button class="btn btn-secondary btn-sm" onclick="viewClientInvoices('${client.id}')">
                    <i class="fas fa-file-invoice"></i> Faktúry
                </button>
                <button class="btn btn-primary btn-sm" onclick="createInvoiceForClient('${client.id}')">
                    <i class="fas fa-plus"></i> Nová faktúra
                </button>
                <button class="btn btn-danger btn-sm" onclick="deleteClient('${client.id}')">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `).join('');
}

function initializeClientFilters() {
    const searchInput = document.getElementById('clientSearch');
    const sortSelect = document.getElementById('clientSort');

    if (searchInput) {
        searchInput.addEventListener('input', updateClientsDisplay);
    }

    if (sortSelect) {
        sortSelect.addEventListener('change', updateClientsDisplay);
    }
}

function showAddClientModal() {
    // Create modal HTML if it doesn't exist
    let modal = document.getElementById('addClientModal');
    if (!modal) {
        modal = createClientModal();
        document.body.appendChild(modal);
    }

    // Reset form
    document.getElementById('addClientForm').reset();
    document.getElementById('clientModalTitle').textContent = 'Nový klient';
    document.getElementById('addClientForm').dataset.mode = 'add';

    modal.style.display = 'flex';
}

function createClientModal() {
    const modal = document.createElement('div');
    modal.id = 'addClientModal';
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="clientModalTitle"><i class="fas fa-user-plus"></i> Nový klient</h3>
                <span class="close" onclick="closeModal('addClientModal')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="addClientForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label>Meno a priezvisko *</label>
                            <input type="text" id="clientName" required>
                        </div>
                        <div class="form-group">
                            <label>Telefón *</label>
                            <input type="tel" id="clientPhone" required>
                        </div>
                        <div class="form-group">
                            <label>Email *</label>
                            <input type="email" id="clientEmail" required>
                        </div>
                        <div class="form-group">
                            <label>Adresa</label>
                            <input type="text" id="clientAddress">
                        </div>
                        <div class="form-group">
                            <label>IČO (pre firmy)</label>
                            <input type="text" id="clientIco">
                        </div>
                        <div class="form-group">
                            <label>DIČ (pre firmy)</label>
                            <input type="text" id="clientDic">
                        </div>
                        <div class="form-group full-width">
                            <label>Poznámky</label>
                            <textarea id="clientNotes" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('addClientModal')">
                            Zrušiť
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Uložiť
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;

    // Add form submit handler
    modal.querySelector('#addClientForm').addEventListener('submit', handleClientSubmit);

    return modal;
}

function handleClientSubmit(e) {
    e.preventDefault();

    const form = e.target;
    const mode = form.dataset.mode;
    const clientId = form.dataset.clientId;

    const clientData = {
        name: document.getElementById('clientName').value,
        phone: document.getElementById('clientPhone').value,
        email: document.getElementById('clientEmail').value,
        address: document.getElementById('clientAddress').value,
        ico: document.getElementById('clientIco').value,
        dic: document.getElementById('clientDic').value,
        notes: document.getElementById('clientNotes').value
    };

    if (mode === 'add') {
        const newClient = new Client(clientData);
        clients.push(newClient);
        showNotification('Klient bol úspešne pridaný', 'success');
    } else if (mode === 'edit') {
        const clientIndex = clients.findIndex(c => c.id === clientId);
        if (clientIndex !== -1) {
            Object.assign(clients[clientIndex], clientData);
            showNotification('Klient bol úspešne aktualizovaný', 'success');
        }
    }

    saveClientsData();
    updateClientsDisplay();
    updateCRMStats();
    closeModal('addClientModal');
}

function editClient(clientId) {
    const client = clients.find(c => c.id === clientId);
    if (!client) return;

    // Show modal
    showAddClientModal();

    // Fill form with client data
    document.getElementById('clientName').value = client.name;
    document.getElementById('clientPhone').value = client.phone;
    document.getElementById('clientEmail').value = client.email;
    document.getElementById('clientAddress').value = client.address || '';
    document.getElementById('clientIco').value = client.ico || '';
    document.getElementById('clientDic').value = client.dic || '';
    document.getElementById('clientNotes').value = client.notes || '';

    // Set form to edit mode
    document.getElementById('clientModalTitle').textContent = 'Upraviť klienta';
    document.getElementById('addClientForm').dataset.mode = 'edit';
    document.getElementById('addClientForm').dataset.clientId = clientId;
}

function deleteClient(clientId) {
    const client = clients.find(c => c.id === clientId);
    if (!client) return;

    if (confirm(`Naozaj chcete vymazať klienta "${client.name}"? Táto akcia sa nedá vrátiť späť.`)) {
        clients = clients.filter(c => c.id !== clientId);
        saveClientsData();
        updateClientsDisplay();
        updateCRMStats();
        showNotification('Klient bol vymazaný', 'success');
    }
}

function viewClientInvoices(clientId) {
    // Switch to invoices tab and filter by client
    switchTab('invoices');

    // Set client filter (we'll implement this in invoice functions)
    setTimeout(() => {
        const clientFilter = document.getElementById('invoiceClientFilter');
        if (clientFilter) {
            clientFilter.value = clientId;
            updateInvoicesDisplay();
        }
    }, 100);
}

function createInvoiceForClient(clientId) {
    // Switch to invoices tab and open new invoice modal with pre-filled client
    switchTab('invoices');

    setTimeout(() => {
        showAddInvoiceModal(clientId);
    }, 100);
}

function updateCRMStats() {
    // Update client stats
    const totalClients = clients.length;
    const totalRevenue = clients.reduce((sum, client) => sum + client.totalRevenue, 0);
    const activeContracts = clients.reduce((sum, client) => sum + client.activeContracts, 0);

    // Update invoice stats
    const totalInvoicesCount = invoices.length;
    const paidInvoicesCount = invoices.filter(inv => inv.status === 'paid').length;
    const pendingInvoicesCount = invoices.filter(inv => inv.status === 'pending').length;
    const totalInvoiceAmount = invoices.reduce((sum, inv) => sum + inv.total, 0);

    // Update DOM elements
    updateElementText('totalClients', totalClients);
    updateElementText('totalClientRevenue', formatCurrency(totalRevenue));
    updateElementText('activeClientContracts', activeContracts);

    updateElementText('totalInvoices', totalInvoicesCount);
    updateElementText('paidInvoices', paidInvoicesCount);
    updateElementText('pendingInvoicesCount', pendingInvoicesCount);
    updateElementText('totalInvoiceAmount', formatCurrency(totalInvoiceAmount));
}

function updateElementText(id, text) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = text;
    }
}

// Invoice management functions
function updateInvoicesDisplay() {
    const invoicesList = document.getElementById('invoicesList');
    if (!invoicesList) return;

    if (invoices.length === 0) {
        invoicesList.innerHTML = `
            <div class="no-invoices">
                <i class="fas fa-file-invoice"></i>
                <h4>Žiadne faktúry</h4>
                <p>Začnite vytvorením novej faktúry</p>
                <button class="btn btn-primary" onclick="showAddInvoiceModal()">
                    <i class="fas fa-plus"></i> Vytvoriť prvú faktúru
                </button>
            </div>
        `;
        return;
    }

    const statusFilter = document.getElementById('invoiceStatusFilter')?.value || 'all';
    const yearFilter = document.getElementById('invoiceYearFilter')?.value || 'all';
    const searchTerm = document.getElementById('invoiceSearch')?.value.toLowerCase() || '';

    let filteredInvoices = invoices.filter(invoice => {
        const matchesStatus = statusFilter === 'all' || invoice.status === statusFilter;
        const matchesYear = yearFilter === 'all' || new Date(invoice.issueDate).getFullYear().toString() === yearFilter;
        const matchesSearch = invoice.number.toLowerCase().includes(searchTerm) ||
                             getClientName(invoice.clientId).toLowerCase().includes(searchTerm);

        return matchesStatus && matchesYear && matchesSearch;
    });

    // Sort by invoice number (newest first)
    filteredInvoices.sort((a, b) => b.number.localeCompare(a.number));

    invoicesList.innerHTML = filteredInvoices.map(invoice => {
        const client = clients.find(c => c.id === invoice.clientId);
        const clientName = client ? client.name : 'Neznámy klient';
        const statusClass = getInvoiceStatusClass(invoice);
        const statusText = getInvoiceStatusText(invoice);

        return `
            <div class="invoice-item ${statusClass}" data-invoice-id="${invoice.id}">
                <div class="invoice-header">
                    <div class="invoice-number">
                        <i class="fas fa-file-invoice"></i>
                        <span class="number">${invoice.number}</span>
                    </div>
                    <div class="invoice-client">
                        <h4>${clientName}</h4>
                        <span class="invoice-date">${formatDate(invoice.issueDate)}</span>
                    </div>
                    <div class="invoice-amount">
                        <span class="amount">${formatCurrency(invoice.total)}</span>
                        <span class="status ${invoice.status}">${statusText}</span>
                    </div>
                </div>
                <div class="invoice-details">
                    <div class="detail-item">
                        <i class="fas fa-calendar"></i>
                        <span>Splatnosť: ${formatDate(invoice.dueDate)}</span>
                    </div>
                    ${invoice.paidDate ? `
                        <div class="detail-item">
                            <i class="fas fa-check"></i>
                            <span>Uhradené: ${formatDate(invoice.paidDate)}</span>
                        </div>
                    ` : ''}
                </div>
                <div class="invoice-actions">
                    <button class="btn btn-secondary btn-sm" onclick="viewInvoice('${invoice.id}')">
                        <i class="fas fa-eye"></i> Zobraziť
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="generateInvoicePDF('${invoice.id}')">
                        <i class="fas fa-file-pdf"></i> PDF
                    </button>
                    ${invoice.status === 'pending' ? `
                        <button class="btn btn-success btn-sm" onclick="markInvoiceAsPaid('${invoice.id}')">
                            <i class="fas fa-check"></i> Označiť ako uhradenú
                        </button>
                    ` : ''}
                    <button class="btn btn-primary btn-sm" onclick="editInvoice('${invoice.id}')">
                        <i class="fas fa-edit"></i> Upraviť
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="deleteInvoice('${invoice.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }).join('');
}

function getClientName(clientId) {
    const client = clients.find(c => c.id === clientId);
    return client ? client.name : 'Neznámy klient';
}

function getInvoiceStatusClass(invoice) {
    if (invoice.status === 'paid') return 'invoice-paid';
    if (invoice.status === 'overdue' || (invoice.status === 'pending' && new Date(invoice.dueDate) < new Date())) {
        return 'invoice-overdue';
    }
    return 'invoice-pending';
}

function getInvoiceStatusText(invoice) {
    switch(invoice.status) {
        case 'paid': return 'Uhradená';
        case 'overdue': return 'Po splatnosti';
        case 'pending':
            if (new Date(invoice.dueDate) < new Date()) {
                return 'Po splatnosti';
            }
            return 'Čaká na úhradu';
        default: return 'Neznámy stav';
    }
}

function initializeInvoiceFilters() {
    const statusFilter = document.getElementById('invoiceStatusFilter');
    const yearFilter = document.getElementById('invoiceYearFilter');
    const searchInput = document.getElementById('invoiceSearch');

    if (statusFilter) {
        statusFilter.addEventListener('change', updateInvoicesDisplay);
    }

    if (yearFilter) {
        yearFilter.addEventListener('change', updateInvoicesDisplay);
    }

    if (searchInput) {
        searchInput.addEventListener('input', updateInvoicesDisplay);
    }
}

function showAddInvoiceModal(preselectedClientId = null) {
    // Create modal HTML if it doesn't exist
    let modal = document.getElementById('addInvoiceModal');
    if (!modal) {
        modal = createInvoiceModal();
        document.body.appendChild(modal);
    }

    // Reset form
    document.getElementById('addInvoiceForm').reset();
    document.getElementById('invoiceModalTitle').textContent = 'Nová faktúra';
    document.getElementById('addInvoiceForm').dataset.mode = 'add';

    // Pre-select client if provided
    if (preselectedClientId) {
        const clientSelect = document.getElementById('invoiceClientSelect');
        if (clientSelect) {
            clientSelect.value = preselectedClientId;
        }
    }

    // Set default dates
    document.getElementById('invoiceIssueDate').value = new Date().toISOString().split('T')[0];
    document.getElementById('invoiceDueDate').value = calculateDueDate(2);

    // Generate invoice number
    document.getElementById('invoiceNumber').value = generateInvoiceNumber();

    modal.style.display = 'flex';

    // Populate client select
    populateClientSelect();
    updateInvoiceItemsDisplay();
}

function createInvoiceModal() {
    const modal = document.createElement('div');
    modal.id = 'addInvoiceModal';
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h3 id="invoiceModalTitle"><i class="fas fa-file-invoice-dollar"></i> Nová faktúra</h3>
                <span class="close" onclick="closeModal('addInvoiceModal')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="addInvoiceForm">
                    <div class="invoice-form-layout">
                        <div class="invoice-basic-info">
                            <h4>Základné údaje</h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label>Číslo faktúry</label>
                                    <input type="text" id="invoiceNumber" readonly>
                                </div>
                                <div class="form-group">
                                    <label>Dátum vystavenia</label>
                                    <input type="date" id="invoiceIssueDate" required>
                                </div>
                                <div class="form-group">
                                    <label>Dátum splatnosti</label>
                                    <input type="date" id="invoiceDueDate" required>
                                </div>
                                <div class="form-group">
                                    <label>Odberateľ *</label>
                                    <select id="invoiceClientSelect" required>
                                        <option value="">Vyberte klienta</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="invoice-items-section">
                            <h4>Položky faktúry</h4>
                            <div class="invoice-items-header">
                                <button type="button" class="btn btn-secondary btn-sm" onclick="addInvoiceItem()">
                                    <i class="fas fa-plus"></i> Pridať položku
                                </button>
                                <button type="button" class="btn btn-primary btn-sm" onclick="importFromQuote()">
                                    <i class="fas fa-file-import"></i> Importovať z cenovej ponuky
                                </button>
                                <button type="button" class="btn btn-info btn-sm" onclick="showAvailableServices()">
                                    <i class="fas fa-list"></i> Dostupné služby
                                </button>
                            </div>
                            <div class="invoice-items-list" id="invoiceItemsList">
                                <!-- Items will be populated by JavaScript -->
                            </div>
                        </div>

                        <div class="invoice-summary">
                            <h4>Súčet</h4>
                            <div class="summary-row">
                                <span>Základ bez DPH:</span>
                                <span id="invoiceSubtotal">0,00 EUR</span>
                            </div>
                            <div class="summary-row">
                                <span>DPH 20%:</span>
                                <span id="invoiceVAT">0,00 EUR</span>
                            </div>
                            <div class="summary-row total">
                                <span>Celkom s DPH:</span>
                                <span id="invoiceTotal">0,00 EUR</span>
                            </div>
                        </div>

                        <div class="invoice-notes">
                            <div class="form-group">
                                <label>Poznámka</label>
                                <textarea id="invoiceNotes" rows="3"></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="modal-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('addInvoiceModal')">
                            Zrušiť
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Uložiť faktúru
                        </button>
                        <button type="button" class="btn btn-success" onclick="saveAndGeneratePDF()">
                            <i class="fas fa-file-pdf"></i> Uložiť a generovať PDF
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;

    // Add form submit handler
    modal.querySelector('#addInvoiceForm').addEventListener('submit', handleInvoiceSubmit);

    return modal;
}

function populateClientSelect() {
    const clientSelect = document.getElementById('invoiceClientSelect');
    if (!clientSelect) return;

    clientSelect.innerHTML = '<option value="">Vyberte klienta</option>';

    clients.forEach(client => {
        const option = document.createElement('option');
        option.value = client.id;
        option.textContent = `${client.name} (${client.phone})`;
        clientSelect.appendChild(option);
    });
}

let invoiceItems = [];

function addInvoiceItem() {
    const newItem = {
        id: Date.now(),
        serviceId: '',
        name: '',
        price: 0,
        quantity: 1,
        total: 0
    };

    invoiceItems.push(newItem);
    updateInvoiceItemsDisplay();
}

function removeInvoiceItem(itemId) {
    invoiceItems = invoiceItems.filter(item => item.id !== itemId);
    updateInvoiceItemsDisplay();
}

function updateInvoiceItemsDisplay() {
    const itemsList = document.getElementById('invoiceItemsList');
    if (!itemsList) return;

    if (invoiceItems.length === 0) {
        itemsList.innerHTML = `
            <div class="no-items">
                <p>Žiadne položky. Kliknite na "Pridať položku" alebo "Importovať z cenovej ponuky".</p>
            </div>
        `;
        updateInvoiceTotals();
        return;
    }

    // Get available services from quote system
    const availableServices = getAvailableServicesForInvoice();

    itemsList.innerHTML = invoiceItems.map(item => `
        <div class="invoice-item-row" data-item-id="${item.id}">
            <select onchange="updateItemService(${item.id}, this.value)">
                <option value="">Vyberte službu</option>
                ${availableServices.map(service =>
                    `<option value="${service.id}" ${item.serviceId === service.id ? 'selected' : ''}>${service.name}</option>`
                ).join('')}
                <optgroup label="Vlastné služby">
                    ${Object.entries(SERVICES).map(([key, service]) =>
                        `<option value="${key}" ${item.serviceId === key ? 'selected' : ''}>${service.name}</option>`
                    ).join('')}
                </optgroup>
            </select>
            <input type="number" step="0.01" min="0" value="${item.price}"
                   onchange="updateItemPrice(${item.id}, this.value)" placeholder="Cena">
            <input type="number" min="1" value="${item.quantity}"
                   onchange="updateItemQuantity(${item.id}, this.value)" placeholder="Počet">
            <span class="item-total">${formatCurrency(item.total)}</span>
            <button type="button" class="remove-item" onclick="removeInvoiceItem(${item.id})">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `).join('');

    updateInvoiceTotals();
}

function updateItemService(itemId, serviceId) {
    const item = invoiceItems.find(i => i.id === itemId);
    if (!item) return;

    item.serviceId = serviceId;

    // Try to find service in quote system first
    const availableServices = getAvailableServicesForInvoice();
    const quoteService = availableServices.find(s => s.id === serviceId);

    if (quoteService) {
        item.name = quoteService.name;
        item.price = quoteService.price;
    } else if (serviceId && SERVICES[serviceId]) {
        item.name = SERVICES[serviceId].name;
        item.price = SERVICES[serviceId].price;
    } else {
        item.name = '';
        item.price = 0;
    }

    item.total = item.price * item.quantity;
    updateInvoiceItemsDisplay();
}

function updateItemPrice(itemId, price) {
    const item = invoiceItems.find(i => i.id === itemId);
    if (!item) return;

    item.price = parseFloat(price) || 0;
    item.total = item.price * item.quantity;
    updateInvoiceItemsDisplay();
}

function updateItemQuantity(itemId, quantity) {
    const item = invoiceItems.find(i => i.id === itemId);
    if (!item) return;

    item.quantity = parseInt(quantity) || 1;
    item.total = item.price * item.quantity;
    updateInvoiceItemsDisplay();
}

function updateInvoiceTotals() {
    const subtotal = invoiceItems.reduce((sum, item) => sum + item.total, 0);
    const vat = calculateVAT(subtotal);
    const total = subtotal + vat;

    updateElementText('invoiceSubtotal', formatCurrency(subtotal));
    updateElementText('invoiceVAT', formatCurrency(vat));
    updateElementText('invoiceTotal', formatCurrency(total));
}

function handleInvoiceSubmit(e) {
    e.preventDefault();

    const form = e.target;
    const mode = form.dataset.mode;
    const invoiceId = form.dataset.invoiceId;

    const clientId = document.getElementById('invoiceClientSelect').value;
    if (!clientId) {
        alert('Prosím vyberte klienta');
        return;
    }

    if (invoiceItems.length === 0) {
        alert('Prosím pridajte aspoň jednu položku');
        return;
    }

    const subtotal = invoiceItems.reduce((sum, item) => sum + item.total, 0);
    const vat = calculateVAT(subtotal);
    const total = subtotal + vat;

    const invoiceData = {
        clientId: clientId,
        issueDate: document.getElementById('invoiceIssueDate').value,
        dueDate: document.getElementById('invoiceDueDate').value,
        items: invoiceItems.map(item => new InvoiceItem({
            serviceId: item.serviceId,
            name: item.name || SERVICES[item.serviceId]?.name || 'Vlastná služba',
            price: item.price,
            quantity: item.quantity,
            total: item.total
        })),
        subtotal: subtotal,
        vat: vat,
        total: total,
        notes: document.getElementById('invoiceNotes').value
    };

    if (mode === 'add') {
        const newInvoice = new Invoice(invoiceData);
        invoices.push(newInvoice);
        showNotification('Faktúra bola úspešne vytvorená', 'success');
    } else if (mode === 'edit') {
        const invoiceIndex = invoices.findIndex(inv => inv.id === invoiceId);
        if (invoiceIndex !== -1) {
            Object.assign(invoices[invoiceIndex], invoiceData);
            showNotification('Faktúra bola úspešne aktualizovaná', 'success');
        }
    }

    saveInvoicesData();
    updateInvoicesDisplay();
    updateCRMStats();
    closeModal('addInvoiceModal');

    // Reset invoice items
    invoiceItems = [];
}

function saveAndGeneratePDF() {
    // First save the invoice
    const form = document.getElementById('addInvoiceForm');
    const submitEvent = new Event('submit');
    form.dispatchEvent(submitEvent);

    // Then generate PDF for the last created invoice
    setTimeout(() => {
        if (invoices.length > 0) {
            const lastInvoice = invoices[invoices.length - 1];
            generateInvoicePDF(lastInvoice.id);
        }
    }, 100);
}

// Invoice actions
function viewInvoice(invoiceId) {
    const invoice = invoices.find(inv => inv.id === invoiceId);
    if (!invoice) return;

    // Create and show invoice detail modal
    showInvoiceDetailModal(invoice);
}

function editInvoice(invoiceId) {
    const invoice = invoices.find(inv => inv.id === invoiceId);
    if (!invoice) return;

    // Show modal
    showAddInvoiceModal();

    // Fill form with invoice data
    document.getElementById('invoiceNumber').value = invoice.number;
    document.getElementById('invoiceIssueDate').value = invoice.issueDate;
    document.getElementById('invoiceDueDate').value = invoice.dueDate;
    document.getElementById('invoiceClientSelect').value = invoice.clientId;
    document.getElementById('invoiceNotes').value = invoice.notes || '';

    // Set invoice items
    invoiceItems = invoice.items.map(item => ({
        id: Date.now() + Math.random(),
        serviceId: item.serviceId,
        name: item.name,
        price: item.price,
        quantity: item.quantity,
        total: item.total
    }));

    // Set form to edit mode
    document.getElementById('invoiceModalTitle').textContent = 'Upraviť faktúru';
    document.getElementById('addInvoiceForm').dataset.mode = 'edit';
    document.getElementById('addInvoiceForm').dataset.invoiceId = invoiceId;

    updateInvoiceItemsDisplay();
}

function deleteInvoice(invoiceId) {
    const invoice = invoices.find(inv => inv.id === invoiceId);
    if (!invoice) return;

    if (confirm(`Naozaj chcete vymazať faktúru ${invoice.number}? Táto akcia sa nedá vrátiť späť.`)) {
        invoices = invoices.filter(inv => inv.id !== invoiceId);
        saveInvoicesData();
        updateInvoicesDisplay();
        updateCRMStats();
        showNotification('Faktúra bola vymazaná', 'success');
    }
}

function markInvoiceAsPaid(invoiceId) {
    const invoice = invoices.find(inv => inv.id === invoiceId);
    if (!invoice) return;

    invoice.status = 'paid';
    invoice.paidDate = new Date().toISOString().split('T')[0];

    saveInvoicesData();
    updateInvoicesDisplay();
    updateCRMStats();
    showNotification(`Faktúra ${invoice.number} bola označená ako uhradená`, 'success');
}

function generateInvoicePDF(invoiceId) {
    const invoice = invoices.find(inv => inv.id === invoiceId);
    if (!invoice) {
        alert('Faktúra nebola nájdená');
        return;
    }

    const client = clients.find(c => c.id === invoice.clientId);
    if (!client) {
        alert('Klient pre túto faktúru nebol nájdený');
        return;
    }

    // Check if html2pdf is available, if not use print fallback
    if (typeof window.html2pdf === 'undefined') {
        console.warn('html2pdf not available, using print fallback');
        generateInvoicePDFPrint(invoice, client);
        return;
    }

    try {
        // Create PDF content
        const pdfContent = createInvoicePDFContent(invoice, client);

        // Create temporary container for PDF content
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = pdfContent;
        tempDiv.style.position = 'absolute';
        tempDiv.style.left = '-9999px';
        tempDiv.style.top = '-9999px';
        document.body.appendChild(tempDiv);

        // PDF options optimized for Electron
        const opt = {
            margin: [0.5, 0.5, 0.5, 0.5],
            filename: `faktura_${invoice.number}.pdf`,
            image: {
                type: 'jpeg',
                quality: 0.95
            },
            html2canvas: {
                scale: 2,
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff'
            },
            jsPDF: {
                unit: 'in',
                format: 'a4',
                orientation: 'portrait',
                compress: true
            }
        };

        // Generate PDF
        window.html2pdf().set(opt).from(tempDiv).save().then(() => {
            // Clean up
            document.body.removeChild(tempDiv);
            showNotification(`PDF faktúra ${invoice.number} bola vygenerovaná`, 'success');
        }).catch((error) => {
            console.error('PDF generation error:', error);
            document.body.removeChild(tempDiv);
            alert('Chyba pri generovaní PDF: ' + error.message);
        });

    } catch (error) {
        console.error('PDF generation error:', error);
        alert('Chyba pri generovaní PDF: ' + error.message);
    }
}

function createInvoicePDFContent(invoice, client) {
    const today = new Date().toLocaleDateString('sk-SK');

    return `
        <div style="font-family: Arial, sans-serif; width: 210mm; min-height: 297mm; margin: 0; padding: 15mm; box-sizing: border-box; background: white;">
            <!-- Header -->
            <div style="text-align: center; margin-bottom: 25px; border-bottom: 2px solid #5e2e60; padding-bottom: 15px;">
                <h1 style="color: #5e2e60; margin: 0 0 15px 0; font-size: 28px; font-weight: bold;">FAKTÚRA</h1>
                <div style="display: table; width: 100%;">
                    <div style="display: table-cell; text-align: left; vertical-align: top; width: 50%;">
                        <div style="color: #5e2e60; font-weight: bold; font-size: 16px; margin-bottom: 5px;">eSpomienka</div>
                        <div style="font-size: 12px; color: #666;">Starostlivosť o hrobové miesta</div>
                    </div>
                    <div style="display: table-cell; text-align: right; vertical-align: top; width: 50%;">
                        <div style="font-weight: bold; font-size: 14px; line-height: 1.4;">
                            ${COMPANY_DATA.name}<br>
                            ${COMPANY_DATA.director}<br>
                            ${COMPANY_DATA.address}<br>
                            ${COMPANY_DATA.city}<br>
                            IČO: ${COMPANY_DATA.ico}<br>
                            DIČ: ${COMPANY_DATA.dic}<br>
                            IČ DPH: ${COMPANY_DATA.icDph}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Invoice Details -->
            <div style="margin-bottom: 25px;">
                <div style="display: table; width: 100%; font-size: 14px;">
                    <div style="display: table-cell; width: 50%; vertical-align: top;">
                        <div style="margin-bottom: 8px;"><strong>Číslo faktúry:</strong> ${invoice.number}</div>
                        <div style="margin-bottom: 8px;"><strong>Dátum vystavenia:</strong> ${formatDate(invoice.issueDate)}</div>
                        <div style="margin-bottom: 8px;"><strong>Dátum splatnosti:</strong> ${formatDate(invoice.dueDate)}</div>
                        <div><strong>Forma úhrady:</strong> Bezhotovostne</div>
                    </div>
                </div>
            </div>

            <!-- Client Information -->
            <div style="margin-bottom: 25px; border: 2px solid #5e2e60; padding: 15px; border-radius: 5px;">
                <h3 style="color: #5e2e60; margin: 0 0 10px 0; font-size: 16px;">ODBERATEĽ:</h3>
                <div style="font-size: 14px; line-height: 1.4;">
                    <strong style="font-size: 15px;">${client.name}</strong><br>
                    ${client.address ? `${client.address}<br>` : ''}
                    ${client.phone ? `Tel: ${client.phone}<br>` : ''}
                    ${client.email ? `Email: ${client.email}<br>` : ''}
                    ${client.ico ? `IČO: ${client.ico}<br>` : ''}
                    ${client.dic ? `DIČ: ${client.dic}` : ''}
                </div>
            </div>

            <!-- Services Table -->
            <div style="margin-bottom: 25px;">
                <h3 style="color: #5e2e60; margin-bottom: 15px; font-size: 16px;">DODANÉ SLUŽBY:</h3>
                <table style="width: 100%; border-collapse: collapse; border: 2px solid #5e2e60; font-size: 13px;">
                    <thead>
                        <tr style="background-color: #5e2e60; color: white;">
                            <th style="border: 1px solid #5e2e60; padding: 12px; text-align: left; font-weight: bold;">Názov služby</th>
                            <th style="border: 1px solid #5e2e60; padding: 12px; text-align: center; font-weight: bold; width: 80px;">Cena</th>
                            <th style="border: 1px solid #5e2e60; padding: 12px; text-align: center; font-weight: bold; width: 60px;">MJ</th>
                            <th style="border: 1px solid #5e2e60; padding: 12px; text-align: right; font-weight: bold; width: 80px;">Celkom</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${invoice.items.map(item => `
                            <tr style="background-color: white;">
                                <td style="border: 1px solid #ddd; padding: 10px; vertical-align: top;">${item.name}</td>
                                <td style="border: 1px solid #ddd; padding: 10px; text-align: center; vertical-align: top;">${formatCurrency(item.price)}</td>
                                <td style="border: 1px solid #ddd; padding: 10px; text-align: center; vertical-align: top;">${item.quantity} ks</td>
                                <td style="border: 1px solid #ddd; padding: 10px; text-align: right; vertical-align: top; font-weight: bold;">${formatCurrency(item.total)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>

            <!-- Totals -->
            <div style="margin-bottom: 25px;">
                <div style="float: right; width: 300px;">
                    <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                        <tr>
                            <td style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">Základ bez DPH:</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #ddd; font-weight: bold;">${formatCurrency(invoice.subtotal)}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">DPH 20%:</td>
                            <td style="padding: 8px; text-align: right; border-bottom: 1px solid #ddd; font-weight: bold;">${formatCurrency(invoice.vat)}</td>
                        </tr>
                        <tr style="background-color: #5e2e60; color: white;">
                            <td style="padding: 12px; text-align: left; font-weight: bold; font-size: 16px;">CELKOM K ÚHRADE:</td>
                            <td style="padding: 12px; text-align: right; font-weight: bold; font-size: 16px;">${formatCurrency(invoice.total)}</td>
                        </tr>
                    </table>
                </div>
                <div style="clear: both;"></div>
            </div>

            <!-- Bank Details -->
            <div style="margin-bottom: 25px; border: 2px solid #327881; padding: 15px; border-radius: 5px; background-color: #f0f8ff;">
                <div style="font-size: 14px; line-height: 1.6;">
                    <div style="margin-bottom: 8px;"><strong>Číslo účtu:</strong> ${COMPANY_DATA.bankAccount}</div>
                    <div style="margin-bottom: 8px;"><strong>Variabilný symbol:</strong> ${invoice.variableSymbol}</div>
                    <div style="color: #327881; font-weight: bold;">Prosíme o úhradu do ${formatDate(invoice.dueDate)}</div>
                </div>
            </div>

            <!-- Footer -->
            <div style="margin-top: 40px; border-top: 1px solid #ddd; padding-top: 20px;">
                <div style="display: table; width: 100%; font-size: 13px;">
                    <div style="display: table-cell; width: 50%; vertical-align: top;">
                        <div style="margin-bottom: 8px;"><strong>Faktúru vystavil:</strong></div>
                        <div style="font-size: 16px; font-weight: bold; color: #5e2e60;">${COMPANY_DATA.director}</div>
                        <div style="margin-top: 5px; color: #666;">Konateľ spoločnosti</div>
                    </div>
                    <div style="display: table-cell; width: 50%; text-align: right; vertical-align: top;">
                        <div style="margin-bottom: 8px;"><strong>Dátum vystavenia:</strong></div>
                        <div style="font-size: 16px; font-weight: bold;">${today}</div>
                        <div style="margin-top: 15px; font-style: italic; color: #666; font-size: 12px;">
                            [Elektronická faktúra bez podpisu]
                        </div>
                    </div>
                </div>
            </div>

            ${invoice.notes ? `
                <div style="margin-top: 25px; padding: 15px; background-color: #fff9e6; border: 1px solid #ffd700; border-radius: 5px;">
                    <div style="font-weight: bold; margin-bottom: 8px; color: #b8860b;">Poznámka:</div>
                    <div style="font-size: 13px; line-height: 1.5;">${invoice.notes}</div>
                </div>
            ` : ''}

            <!-- Contact Info Footer -->
            <div style="margin-top: 30px; text-align: center; font-size: 12px; color: #666; border-top: 1px solid #eee; padding-top: 15px;">
                <div>${COMPANY_DATA.phone} | ${COMPANY_DATA.email} | ${COMPANY_DATA.website}</div>
                <div style="margin-top: 5px;">Ďakujeme za Vašu dôveru a tešíme sa na ďalšiu spoluprácu</div>
            </div>
        </div>
    `;
}

// Fallback PDF generation using print dialog
function generateInvoicePDFPrint(invoice, client) {
    try {
        // Create PDF content
        const pdfContent = createInvoicePDFContent(invoice, client);

        // Open new window for printing
        const printWindow = window.open('', '_blank', 'width=800,height=600');

        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>Faktúra ${invoice.number}</title>
                <style>
                    @media print {
                        body { margin: 0; }
                        @page { margin: 0.5in; }
                    }
                    body { font-family: Arial, sans-serif; }
                </style>
            </head>
            <body>
                ${pdfContent}
                <script>
                    window.onload = function() {
                        window.print();
                        setTimeout(function() {
                            window.close();
                        }, 1000);
                    };
                </script>
            </body>
            </html>
        `);

        printWindow.document.close();

        showNotification(`Faktúra ${invoice.number} je pripravená na tlač/uloženie ako PDF`, 'success');

    } catch (error) {
        console.error('Print PDF generation error:', error);
        alert('Chyba pri generovaní PDF: ' + error.message);
    }
}

// Services management functions
function updateServicesDisplay() {
    updateBasicServicesGrid();
    updatePackageServicesGrid();
    updateDigitalServicesGrid();
}

function updateBasicServicesGrid() {
    const grid = document.getElementById('basicServicesGrid');
    if (!grid) return;

    const basicServices = Object.entries(SERVICES).filter(([key, service]) => service.category === 'basic');

    grid.innerHTML = basicServices.map(([key, service]) => `
        <div class="service-edit-item">
            <div class="service-header">
                <div class="service-name">${service.name}</div>
                <div class="service-actions">
                    <button class="btn btn-sm btn-secondary" onclick="editService('${key}')" title="Upraviť">
                        <i class="fas fa-edit"></i>
                    </button>
                    ${service.custom ? `
                        <button class="btn btn-sm btn-danger" onclick="deleteService('${key}')" title="Vymazať">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : ''}
                </div>
            </div>
            <div class="service-price-input">
                <input type="number" step="0.01" min="0" value="${service.price}"
                       onchange="updateServicePrice('${key}', this.value)">
                <span>EUR</span>
            </div>
            ${service.description ? `<div class="service-description">${service.description}</div>` : ''}
        </div>
    `).join('');
}

function updatePackageServicesGrid() {
    const grid = document.getElementById('packageServicesGrid');
    if (!grid) return;

    const packageServices = Object.entries(SERVICES).filter(([key, service]) => service.category === 'package');

    grid.innerHTML = packageServices.map(([key, service]) => `
        <div class="service-edit-item">
            <div class="service-header">
                <div class="service-name">${service.name}</div>
                <div class="service-actions">
                    <button class="btn btn-sm btn-secondary" onclick="editService('${key}')" title="Upraviť">
                        <i class="fas fa-edit"></i>
                    </button>
                    ${service.custom ? `
                        <button class="btn btn-sm btn-danger" onclick="deleteService('${key}')" title="Vymazať">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : ''}
                </div>
            </div>
            <div class="service-price-input">
                <input type="number" step="0.01" min="0" value="${service.price}"
                       onchange="updateServicePrice('${key}', this.value)">
                <span>EUR/${service.period || 'rok'}</span>
            </div>
            ${service.description ? `<div class="service-description">${service.description}</div>` : ''}
        </div>
    `).join('');
}

function updateDigitalServicesGrid() {
    const grid = document.getElementById('digitalServicesGrid');
    if (!grid) return;

    const digitalServices = Object.entries(SERVICES).filter(([key, service]) => service.category === 'digital');

    grid.innerHTML = digitalServices.map(([key, service]) => `
        <div class="service-edit-item">
            <div class="service-header">
                <div class="service-name">${service.name}</div>
                <div class="service-actions">
                    <button class="btn btn-sm btn-secondary" onclick="editService('${key}')" title="Upraviť">
                        <i class="fas fa-edit"></i>
                    </button>
                    ${service.custom ? `
                        <button class="btn btn-sm btn-danger" onclick="deleteService('${key}')" title="Vymazať">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : ''}
                </div>
            </div>
            <div class="service-price-input">
                <input type="number" step="0.01" min="0" value="${service.price}"
                       onchange="updateServicePrice('${key}', this.value)">
                <span>EUR/${service.period || 'rok'}</span>
            </div>
            ${service.description ? `<div class="service-description">${service.description}</div>` : ''}
        </div>
    `).join('');
}

function updateServicePrice(serviceKey, newPrice) {
    if (SERVICES[serviceKey]) {
        SERVICES[serviceKey].price = parseFloat(newPrice) || 0;
        localStorage.setItem('crm_services', JSON.stringify(SERVICES));
        showNotification('Cena služby bola aktualizovaná', 'success');
    }
}

function resetServicePrices() {
    if (confirm('Naozaj chcete obnoviť všetky ceny služieb na predvolené hodnoty?')) {
        localStorage.removeItem('crm_services');
        location.reload(); // Reload to get default prices
    }
}

// Utility functions
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Show notification
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateY(0)';
    }, 100);

    // Hide and remove notification
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateY(-100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
    }
}

// Integration with existing order system
function createInvoiceFromOrder(orderId) {
    // This function will be called from the orders system
    // to create an invoice from a completed order

    // Find the order (assuming orders array exists from orders.js)
    if (typeof orders !== 'undefined') {
        const order = orders.find(o => o.id === orderId);
        if (!order) return;

        // Find or create client
        let client = clients.find(c =>
            c.name === order.customerName &&
            c.phone === order.customerPhone
        );

        if (!client) {
            // Create new client from order data
            client = new Client({
                name: order.customerName,
                phone: order.customerPhone,
                email: order.customerEmail || '',
                address: order.customerAddress || ''
            });
            clients.push(client);
            saveClientsData();
        }

        // Create invoice
        const invoiceData = {
            clientId: client.id,
            orderId: orderId,
            items: [{
                serviceId: order.serviceId || 'custom',
                name: order.serviceName || order.packageName,
                price: order.price || order.totalPrice,
                quantity: 1,
                total: order.price || order.totalPrice
            }]
        };

        const subtotal = invoiceData.items[0].total;
        invoiceData.subtotal = calculateSubtotal(subtotal);
        invoiceData.vat = calculateVAT(invoiceData.subtotal);
        invoiceData.total = subtotal;

        const newInvoice = new Invoice(invoiceData);
        invoices.push(newInvoice);
        saveInvoicesData();

        // Mark order as invoiced
        order.invoiced = true;
        order.invoiceId = newInvoice.id;

        showNotification(`Faktúra ${newInvoice.number} bola vytvorená z objednávky`, 'success');

        return newInvoice.id;
    }
}

// Bulk invoicing functions
function showBulkInvoiceModal() {
    // Get orders ready for invoicing
    const pendingOrders = typeof showPendingInvoiceOrders === 'function' ?
        showPendingInvoiceOrders() : [];

    if (pendingOrders.length === 0) {
        alert('Žiadne objednávky nie sú pripravené na fakturáciu');
        return;
    }

    // Create modal HTML if it doesn't exist
    let modal = document.getElementById('bulkInvoiceModal');
    if (!modal) {
        modal = createBulkInvoiceModal();
        document.body.appendChild(modal);
    }

    // Populate orders list
    populateBulkInvoiceOrders(pendingOrders);

    modal.style.display = 'flex';
}

function createBulkInvoiceModal() {
    const modal = document.createElement('div');
    modal.id = 'bulkInvoiceModal';
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h3><i class="fas fa-layer-group"></i> Hromadné fakturovanie</h3>
                <span class="close" onclick="closeModal('bulkInvoiceModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="bulk-invoice-content">
                    <div class="bulk-invoice-header">
                        <h4>Objednávky pripravené na fakturáciu</h4>
                        <div class="bulk-actions">
                            <button type="button" class="btn btn-secondary" onclick="selectAllOrders()">
                                <i class="fas fa-check-square"></i> Vybrať všetko
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="deselectAllOrders()">
                                <i class="fas fa-square"></i> Zrušiť výber
                            </button>
                        </div>
                    </div>

                    <div class="bulk-orders-list" id="bulkOrdersList">
                        <!-- Orders will be populated by JavaScript -->
                    </div>

                    <div class="bulk-summary">
                        <div class="summary-info">
                            <span>Vybrané: <strong id="selectedOrdersCount">0</strong> objednávok</span>
                            <span>Celková suma: <strong id="selectedOrdersTotal">0,00 EUR</strong></span>
                        </div>
                    </div>
                </div>

                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('bulkInvoiceModal')">
                        Zrušiť
                    </button>
                    <button type="button" class="btn btn-primary" onclick="createBulkInvoices()">
                        <i class="fas fa-file-invoice"></i> Vytvoriť faktúry
                    </button>
                </div>
            </div>
        </div>
    `;

    return modal;
}

function populateBulkInvoiceOrders(orders) {
    const ordersList = document.getElementById('bulkOrdersList');
    if (!ordersList) return;

    ordersList.innerHTML = orders.map(order => {
        const total = typeof getOrderInvoiceTotal === 'function' ?
            getOrderInvoiceTotal(order.id) : 0;

        return `
            <div class="bulk-order-item">
                <label class="bulk-order-label">
                    <input type="checkbox" class="bulk-order-checkbox"
                           data-order-id="${order.id}"
                           data-total="${total}"
                           onchange="updateBulkSummary()">
                    <div class="bulk-order-info">
                        <div class="order-header">
                            <span class="order-id">${order.id}</span>
                            <span class="order-date">${formatDate(order.createdAt || order.startDate)}</span>
                        </div>
                        <div class="order-customer">${order.customer?.name || order.customerName}</div>
                        <div class="order-service">${order.package || 'Služba'}</div>
                        <div class="order-total">${formatCurrency(total)}</div>
                    </div>
                </label>
            </div>
        `;
    }).join('');

    updateBulkSummary();
}

function selectAllOrders() {
    const checkboxes = document.querySelectorAll('.bulk-order-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateBulkSummary();
}

function deselectAllOrders() {
    const checkboxes = document.querySelectorAll('.bulk-order-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateBulkSummary();
}

function updateBulkSummary() {
    const checkboxes = document.querySelectorAll('.bulk-order-checkbox:checked');
    const count = checkboxes.length;
    const total = Array.from(checkboxes).reduce((sum, checkbox) => {
        return sum + parseFloat(checkbox.dataset.total || 0);
    }, 0);

    updateElementText('selectedOrdersCount', count);
    updateElementText('selectedOrdersTotal', formatCurrency(total));
}

function createBulkInvoices() {
    const selectedCheckboxes = document.querySelectorAll('.bulk-order-checkbox:checked');

    if (selectedCheckboxes.length === 0) {
        alert('Prosím vyberte aspoň jednu objednávku');
        return;
    }

    const orderIds = Array.from(selectedCheckboxes).map(checkbox => checkbox.dataset.orderId);
    let createdInvoices = 0;

    orderIds.forEach(orderId => {
        if (typeof createInvoiceFromOrder === 'function') {
            const invoiceId = createInvoiceFromOrder(orderId);
            if (invoiceId) {
                createdInvoices++;
            }
        }
    });

    if (createdInvoices > 0) {
        showNotification(`Vytvorených ${createdInvoices} faktúr`, 'success');
        closeModal('bulkInvoiceModal');
        updateInvoicesDisplay();
        updateCRMStats();

        // Update orders display if available
        if (typeof renderTasks === 'function') {
            renderTasks();
        }
    } else {
        alert('Nepodarilo sa vytvoriť žiadne faktúry');
    }
}

// Invoice detail modal
function showInvoiceDetailModal(invoice) {
    const client = clients.find(c => c.id === invoice.clientId);
    const clientName = client ? client.name : 'Neznámy klient';

    // Create modal HTML if it doesn't exist
    let modal = document.getElementById('invoiceDetailModal');
    if (!modal) {
        modal = createInvoiceDetailModal();
        document.body.appendChild(modal);
    }

    // Populate modal content
    const modalContent = document.getElementById('invoiceDetailContent');
    modalContent.innerHTML = `
        <div class="invoice-detail-layout">
            <div class="invoice-detail-header">
                <div class="invoice-number-large">
                    <i class="fas fa-file-invoice"></i>
                    Faktúra ${invoice.number}
                </div>
                <div class="invoice-status-large ${invoice.status}">
                    ${getInvoiceStatusText(invoice)}
                </div>
            </div>

            <div class="invoice-detail-info">
                <div class="info-section">
                    <h4>Základné údaje</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>Dátum vystavenia:</label>
                            <span>${formatDate(invoice.issueDate)}</span>
                        </div>
                        <div class="info-item">
                            <label>Dátum splatnosti:</label>
                            <span>${formatDate(invoice.dueDate)}</span>
                        </div>
                        ${invoice.paidDate ? `
                            <div class="info-item">
                                <label>Dátum úhrady:</label>
                                <span>${formatDate(invoice.paidDate)}</span>
                            </div>
                        ` : ''}
                        <div class="info-item">
                            <label>Variabilný symbol:</label>
                            <span>${invoice.variableSymbol}</span>
                        </div>
                    </div>
                </div>

                <div class="info-section">
                    <h4>Odberateľ</h4>
                    <div class="client-info">
                        <div class="client-name">${clientName}</div>
                        ${client ? `
                            <div class="client-contact">${client.phone}</div>
                            <div class="client-contact">${client.email}</div>
                            ${client.address ? `<div class="client-address">${client.address}</div>` : ''}
                        ` : ''}
                    </div>
                </div>
            </div>

            <div class="invoice-items-detail">
                <h4>Položky faktúry</h4>
                <table class="invoice-items-table">
                    <thead>
                        <tr>
                            <th>Názov služby</th>
                            <th>Cena</th>
                            <th>Počet</th>
                            <th>Celkom</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${invoice.items.map(item => `
                            <tr>
                                <td>${item.name}</td>
                                <td>${formatCurrency(item.price)}</td>
                                <td>${item.quantity}</td>
                                <td>${formatCurrency(item.total)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>

            <div class="invoice-totals-detail">
                <div class="totals-row">
                    <span>Základ bez DPH:</span>
                    <span>${formatCurrency(invoice.subtotal)}</span>
                </div>
                <div class="totals-row">
                    <span>DPH 20%:</span>
                    <span>${formatCurrency(invoice.vat)}</span>
                </div>
                <div class="totals-row total">
                    <span>Celkom s DPH:</span>
                    <span>${formatCurrency(invoice.total)}</span>
                </div>
            </div>

            ${invoice.notes ? `
                <div class="invoice-notes-detail">
                    <h4>Poznámka</h4>
                    <p>${invoice.notes}</p>
                </div>
            ` : ''}
        </div>
    `;

    modal.style.display = 'flex';
}

function createInvoiceDetailModal() {
    const modal = document.createElement('div');
    modal.id = 'invoiceDetailModal';
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h3><i class="fas fa-file-invoice"></i> Detail faktúry</h3>
                <span class="close" onclick="closeModal('invoiceDetailModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div id="invoiceDetailContent">
                    <!-- Content will be populated by JavaScript -->
                </div>
            </div>
        </div>
    `;

    return modal;
}

// Service management functions
function showAddServiceModal() {
    // Create modal HTML if it doesn't exist
    let modal = document.getElementById('addServiceModal');
    if (!modal) {
        modal = createServiceModal();
        document.body.appendChild(modal);
    }

    // Reset form
    document.getElementById('addServiceForm').reset();
    document.getElementById('serviceModalTitle').textContent = 'Nová služba';
    document.getElementById('addServiceForm').dataset.mode = 'add';

    modal.style.display = 'flex';
}

function createServiceModal() {
    const modal = document.createElement('div');
    modal.id = 'addServiceModal';
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="serviceModalTitle"><i class="fas fa-cogs"></i> Nová služba</h3>
                <span class="close" onclick="closeModal('addServiceModal')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="addServiceForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label>Názov služby *</label>
                            <input type="text" id="serviceName" required>
                        </div>
                        <div class="form-group">
                            <label>Cena (EUR) *</label>
                            <input type="number" step="0.01" min="0" id="servicePrice" required>
                        </div>
                        <div class="form-group">
                            <label>Kategória *</label>
                            <select id="serviceCategory" required>
                                <option value="">Vyberte kategóriu</option>
                                <option value="basic">Základné služby</option>
                                <option value="package">Balíky služieb</option>
                                <option value="digital">Digitálne služby</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Obdobie</label>
                            <input type="text" id="servicePeriod" placeholder="napr. rok, mesiac, 3 roky">
                        </div>
                        <div class="form-group full-width">
                            <label>Popis</label>
                            <textarea id="serviceDescription" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('addServiceModal')">
                            Zrušiť
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Uložiť
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;

    // Add form submit handler
    modal.querySelector('#addServiceForm').addEventListener('submit', handleServiceSubmit);

    return modal;
}

function handleServiceSubmit(e) {
    e.preventDefault();

    const form = e.target;
    const mode = form.dataset.mode;
    const serviceKey = form.dataset.serviceKey;

    const serviceName = document.getElementById('serviceName').value;
    const servicePrice = parseFloat(document.getElementById('servicePrice').value);
    const serviceCategory = document.getElementById('serviceCategory').value;
    const servicePeriod = document.getElementById('servicePeriod').value;
    const serviceDescription = document.getElementById('serviceDescription').value;

    if (mode === 'add') {
        // Generate unique key
        const newKey = serviceName.toLowerCase()
            .replace(/[^a-z0-9]/g, '_')
            .replace(/_+/g, '_')
            .replace(/^_|_$/g, '');

        // Check if key already exists
        if (SERVICES[newKey]) {
            alert('Služba s podobným názvom už existuje');
            return;
        }

        SERVICES[newKey] = {
            name: serviceName,
            price: servicePrice,
            category: serviceCategory,
            period: servicePeriod,
            description: serviceDescription,
            custom: true
        };

        showNotification('Služba bola úspešne pridaná', 'success');
    } else if (mode === 'edit') {
        if (SERVICES[serviceKey]) {
            SERVICES[serviceKey].name = serviceName;
            SERVICES[serviceKey].price = servicePrice;
            SERVICES[serviceKey].category = serviceCategory;
            SERVICES[serviceKey].period = servicePeriod;
            SERVICES[serviceKey].description = serviceDescription;

            showNotification('Služba bola úspešne aktualizovaná', 'success');
        }
    }

    // Save to localStorage
    localStorage.setItem('crm_services', JSON.stringify(SERVICES));

    updateServicesDisplay();
    closeModal('addServiceModal');
}

function editService(serviceKey) {
    const service = SERVICES[serviceKey];
    if (!service) return;

    // Show modal
    showAddServiceModal();

    // Fill form with service data
    document.getElementById('serviceName').value = service.name;
    document.getElementById('servicePrice').value = service.price;
    document.getElementById('serviceCategory').value = service.category;
    document.getElementById('servicePeriod').value = service.period || '';
    document.getElementById('serviceDescription').value = service.description || '';

    // Set form to edit mode
    document.getElementById('serviceModalTitle').textContent = 'Upraviť službu';
    document.getElementById('addServiceForm').dataset.mode = 'edit';
    document.getElementById('addServiceForm').dataset.serviceKey = serviceKey;
}

function deleteService(serviceKey) {
    const service = SERVICES[serviceKey];
    if (!service) return;

    if (!service.custom) {
        alert('Predvolené služby nemožno vymazať. Môžete len upraviť ich cenu.');
        return;
    }

    if (confirm(`Naozaj chcete vymazať službu "${service.name}"? Táto akcia sa nedá vrátiť späť.`)) {
        delete SERVICES[serviceKey];
        localStorage.setItem('crm_services', JSON.stringify(SERVICES));
        updateServicesDisplay();
        showNotification('Služba bola vymazaná', 'success');
    }
}

// Load custom services from localStorage
function loadCustomServices() {
    const savedServices = localStorage.getItem('crm_services');
    if (savedServices) {
        const customServices = JSON.parse(savedServices);
        Object.assign(SERVICES, customServices);
    }
}

// Initialize services on load
document.addEventListener('DOMContentLoaded', function() {
    loadCustomServices();
});

// Functions for integrating with quote system
function getAvailableServicesForInvoice() {
    // Try to get services from quote system
    if (typeof getAllAvailableServices === 'function') {
        return getAllAvailableServices();
    }

    // Fallback: return empty array if quote system not available
    return [];
}

function importFromQuote() {
    // Check if quote system is available
    if (typeof getSelectedServicesData !== 'function') {
        alert('Systém cenovej ponuky nie je dostupný. Služby pridajte manuálne.');
        return;
    }

    const selectedServices = getSelectedServicesData();

    if (selectedServices.length === 0) {
        alert('V cenovej ponuke nie sú vybrané žiadne služby. Prejdite na tab "Cenová ponuka" a vyberte služby.');
        return;
    }

    // Clear existing items
    invoiceItems = [];

    // Add services from quote
    selectedServices.forEach(service => {
        const newItem = {
            id: Date.now() + Math.random(),
            serviceId: service.id,
            name: service.name,
            price: service.price,
            quantity: service.quantity || 1,
            total: service.total || (service.price * (service.quantity || 1))
        };

        invoiceItems.push(newItem);
    });

    updateInvoiceItemsDisplay();
    showNotification(`Importované ${selectedServices.length} služieb z cenovej ponuky`, 'success');
}

function showAvailableServices() {
    const availableServices = getAvailableServicesForInvoice();

    if (availableServices.length === 0) {
        alert('Žiadne služby nie sú dostupné. Systém cenovej ponuky nie je načítaný.');
        return;
    }

    // Create modal with available services
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.id = 'availableServicesModal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-list"></i> Dostupné služby</h3>
                <span class="close" onclick="closeModal('availableServicesModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="services-grid">
                    ${availableServices.map(service => `
                        <div class="service-card">
                            <div class="service-header">
                                <h4>${service.name}</h4>
                                <span class="service-price">${formatCurrency(service.price)}</span>
                            </div>
                            <div class="service-actions">
                                <button class="btn btn-primary btn-sm" onclick="addServiceToInvoice('${service.id}', '${service.name}', ${service.price})">
                                    <i class="fas fa-plus"></i> Pridať
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    modal.style.display = 'flex';
}

function addServiceToInvoice(serviceId, serviceName, servicePrice) {
    const newItem = {
        id: Date.now() + Math.random(),
        serviceId: serviceId,
        name: serviceName,
        price: servicePrice,
        quantity: 1,
        total: servicePrice
    };

    invoiceItems.push(newItem);
    updateInvoiceItemsDisplay();
    closeModal('availableServicesModal');
    showNotification(`Služba "${serviceName}" bola pridaná do faktúry`, 'success');
}

// Export functions for global access
window.showAddClientModal = showAddClientModal;
window.editClient = editClient;
window.deleteClient = deleteClient;
window.viewClientInvoices = viewClientInvoices;
window.createInvoiceForClient = createInvoiceForClient;
window.exportClients = exportClients;

window.showAddInvoiceModal = showAddInvoiceModal;
window.editInvoice = editInvoice;
window.deleteInvoice = deleteInvoice;
window.viewInvoice = viewInvoice;
window.markInvoiceAsPaid = markInvoiceAsPaid;
window.generateInvoicePDF = generateInvoicePDF;
window.showBulkInvoiceModal = showBulkInvoiceModal;

window.showAddServiceModal = showAddServiceModal;
window.editService = editService;
window.deleteService = deleteService;
window.updateServicePrice = updateServicePrice;
window.resetServicePrices = resetServicePrices;

window.addInvoiceItem = addInvoiceItem;
window.removeInvoiceItem = removeInvoiceItem;
window.updateItemService = updateItemService;
window.updateItemPrice = updateItemPrice;
window.updateItemQuantity = updateItemQuantity;
window.saveAndGeneratePDF = saveAndGeneratePDF;
window.importFromQuote = importFromQuote;
window.showAvailableServices = showAvailableServices;

window.selectAllOrders = selectAllOrders;
window.deselectAllOrders = deselectAllOrders;
window.updateBulkSummary = updateBulkSummary;
window.createBulkInvoices = createBulkInvoices;

// Export client functions
function exportClients() {
    if (clients.length === 0) {
        alert('Žiadni klienti na export');
        return;
    }

    const csvData = [
        ['Meno', 'Telefón', 'Email', 'Adresa', 'IČO', 'DIČ', 'Tržby', 'Zmluvy', 'Vytvorené'],
        ...clients.map(client => [
            client.name,
            client.phone,
            client.email,
            client.address || '',
            client.ico || '',
            client.dic || '',
            client.totalRevenue,
            client.activeContracts,
            formatDate(client.createdAt)
        ])
    ];

    const csvContent = csvData.map(row =>
        row.map(field => `"${field}"`).join(',')
    ).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `klienti_${new Date().toISOString().split('T')[0]}.csv`;
    link.click();

    showNotification('Klienti boli exportovaní', 'success');
}

// Automation and workflow functions
function updateInvoiceStatuses() {
    // Check for overdue invoices
    const today = new Date();
    let updatedCount = 0;

    invoices.forEach(invoice => {
        if (invoice.status === 'pending' && new Date(invoice.dueDate) < today) {
            invoice.status = 'overdue';
            updatedCount++;
        }
    });

    if (updatedCount > 0) {
        saveInvoicesData();
        updateInvoicesDisplay();
        updateCRMStats();
    }

    return updatedCount;
}

function updateClientRevenue(clientId) {
    const client = clients.find(c => c.id === clientId);
    if (!client) return;

    // Calculate total revenue from paid invoices
    const clientInvoices = invoices.filter(inv => inv.clientId === clientId && inv.status === 'paid');
    client.totalRevenue = clientInvoices.reduce((sum, inv) => sum + inv.total, 0);

    // Update active contracts (simplified - count active services)
    client.activeContracts = clientInvoices.length > 0 ? 1 : 0;

    saveClientsData();
}

function generateNextInvoiceNumber() {
    const year = new Date().getFullYear();
    const currentYearInvoices = invoices.filter(inv =>
        inv.number.startsWith(year.toString())
    );

    const maxNumber = currentYearInvoices.reduce((max, inv) => {
        const num = parseInt(inv.number.slice(4)); // Remove year prefix
        return num > max ? num : max;
    }, 0);

    const nextNumber = maxNumber + 1;
    return `${year}${String(nextNumber).padStart(3, '0')}`;
}

function autoSaveData() {
    // Auto-save all data every 30 seconds
    saveClientsData();
    saveInvoicesData();

    // Update invoice statuses
    updateInvoiceStatuses();
}

// Set up auto-save interval
setInterval(autoSaveData, 30000); // 30 seconds

// Workflow integration functions
function markOrderAsInvoiced(orderId, invoiceId) {
    if (typeof orders !== 'undefined') {
        const order = orders.find(o => o.id === orderId);
        if (order) {
            order.invoiced = true;
            order.invoiceId = invoiceId;

            // Mark all related tasks as invoiced
            if (typeof tasks !== 'undefined') {
                const orderTasks = tasks.filter(t => t.orderId === orderId);
                orderTasks.forEach(task => {
                    task.invoiced = true;
                    task.invoiceId = invoiceId;
                });
            }

            // Save orders data if function exists
            if (typeof saveOrdersData === 'function') {
                saveOrdersData();
            }
        }
    }
}

function getOrdersReadyForInvoicing() {
    if (typeof orders === 'undefined' || typeof tasks === 'undefined') {
        return [];
    }

    return orders.filter(order => {
        if (order.invoiced) return false;

        const orderTasks = tasks.filter(t => t.orderId === order.id);
        const completedTasks = orderTasks.filter(t => t.status === 'COMPLETED');

        // Order is ready if it has completed tasks
        return completedTasks.length > 0;
    });
}

function calculateOrderTotal(orderId) {
    if (typeof orders === 'undefined' || typeof tasks === 'undefined') {
        return 0;
    }

    const order = orders.find(o => o.id === orderId);
    if (!order) return 0;

    // If order has quote data, use that
    if (order.quoteData && order.quoteData.total) {
        return order.quoteData.total;
    }

    // Otherwise calculate from completed tasks
    const orderTasks = tasks.filter(t => t.orderId === orderId && t.status === 'COMPLETED');
    return orderTasks.reduce((sum, task) => sum + (task.price || 0), 0);
}

// Enhanced createInvoiceFromOrder function
function createInvoiceFromOrderEnhanced(orderId) {
    if (typeof orders === 'undefined') {
        alert('Systém objednávok nie je dostupný');
        return null;
    }

    const order = orders.find(o => o.id === orderId);
    if (!order) {
        alert('Objednávka nebola nájdená');
        return null;
    }

    if (order.invoiced) {
        alert('Objednávka už bola vyfakturovaná');
        return null;
    }

    // Find or create client
    let client = clients.find(c =>
        c.name === order.customerName &&
        c.phone === order.customerPhone
    );

    if (!client) {
        // Create new client from order data
        const clientData = {
            name: order.customerName,
            phone: order.customerPhone,
            email: order.customerEmail || '',
            address: order.customerAddress || ''
        };

        client = new Client(clientData);
        clients.push(client);
        saveClientsData();
    }

    // Prepare invoice items
    const items = [];
    const orderTotal = calculateOrderTotal(orderId);

    if (order.quoteData && order.quoteData.services) {
        // Use services from quote data
        order.quoteData.services.forEach(service => {
            items.push(new InvoiceItem({
                serviceId: 'custom',
                name: service.name,
                price: service.price,
                quantity: 1,
                total: service.price
            }));
        });
    } else {
        // Create single item from order
        items.push(new InvoiceItem({
            serviceId: 'custom',
            name: order.package || order.serviceName || 'Služba',
            price: orderTotal,
            quantity: 1,
            total: orderTotal
        }));
    }

    // Calculate totals
    const subtotalAmount = items.reduce((sum, item) => sum + item.total, 0);
    const subtotal = calculateSubtotal(subtotalAmount);
    const vat = calculateVAT(subtotal);

    // Create invoice
    const invoiceData = {
        clientId: client.id,
        orderId: orderId,
        items: items,
        subtotal: subtotal,
        vat: vat,
        total: subtotalAmount,
        notes: `Faktúra vytvorená z objednávky ${orderId}`
    };

    const newInvoice = new Invoice(invoiceData);
    invoices.push(newInvoice);
    saveInvoicesData();

    // Mark order as invoiced
    markOrderAsInvoiced(orderId, newInvoice.id);

    // Update client revenue
    updateClientRevenue(client.id);

    // Update displays
    updateInvoicesDisplay();
    updateCRMStats();

    showNotification(`Faktúra ${newInvoice.number} bola vytvorená z objednávky ${orderId}`, 'success');

    return newInvoice.id;
}

// Replace the original function
window.createInvoiceFromOrder = createInvoiceFromOrderEnhanced;

// Dashboard integration
function updateDashboardStats() {
    // Update pending invoices count in orders dashboard
    const pendingInvoicesCount = getOrdersReadyForInvoicing().length;
    updateElementText('pendingInvoices', pendingInvoicesCount);

    // Update other CRM stats
    updateCRMStats();
}

// Initialize automation on page load
document.addEventListener('DOMContentLoaded', function() {
    // Initial status update
    setTimeout(() => {
        updateInvoiceStatuses();
        updateDashboardStats();
    }, 1000);

    // Set up periodic updates
    setInterval(updateDashboardStats, 60000); // Update every minute
});

// Export additional functions
window.updateInvoiceStatuses = updateInvoiceStatuses;
window.updateClientRevenue = updateClientRevenue;
window.getOrdersReadyForInvoicing = getOrdersReadyForInvoicing;
window.calculateOrderTotal = calculateOrderTotal;

// Advanced statistics and reporting
function generateAdvancedStats() {
    const stats = {
        clients: {
            total: clients.length,
            newThisMonth: 0,
            topClients: [],
            averageRevenue: 0
        },
        invoices: {
            total: invoices.length,
            paid: 0,
            pending: 0,
            overdue: 0,
            totalAmount: 0,
            paidAmount: 0,
            pendingAmount: 0,
            overdueAmount: 0,
            thisMonth: 0,
            thisMonthAmount: 0
        },
        services: {
            mostPopular: [],
            totalRevenue: {}
        },
        trends: {
            monthlyRevenue: [],
            clientGrowth: []
        }
    };

    const now = new Date();
    const thisMonth = now.getMonth();
    const thisYear = now.getFullYear();

    // Client statistics
    clients.forEach(client => {
        const createdDate = new Date(client.createdAt);
        if (createdDate.getMonth() === thisMonth && createdDate.getFullYear() === thisYear) {
            stats.clients.newThisMonth++;
        }
    });

    stats.clients.averageRevenue = clients.length > 0 ?
        clients.reduce((sum, client) => sum + client.totalRevenue, 0) / clients.length : 0;

    stats.clients.topClients = clients
        .sort((a, b) => b.totalRevenue - a.totalRevenue)
        .slice(0, 5)
        .map(client => ({
            name: client.name,
            revenue: client.totalRevenue
        }));

    // Invoice statistics
    invoices.forEach(invoice => {
        const invoiceDate = new Date(invoice.issueDate);

        stats.invoices.totalAmount += invoice.total;

        switch(invoice.status) {
            case 'paid':
                stats.invoices.paid++;
                stats.invoices.paidAmount += invoice.total;
                break;
            case 'pending':
                stats.invoices.pending++;
                stats.invoices.pendingAmount += invoice.total;
                break;
            case 'overdue':
                stats.invoices.overdue++;
                stats.invoices.overdueAmount += invoice.total;
                break;
        }

        if (invoiceDate.getMonth() === thisMonth && invoiceDate.getFullYear() === thisYear) {
            stats.invoices.thisMonth++;
            stats.invoices.thisMonthAmount += invoice.total;
        }

        // Service statistics
        invoice.items.forEach(item => {
            if (!stats.services.totalRevenue[item.name]) {
                stats.services.totalRevenue[item.name] = 0;
            }
            stats.services.totalRevenue[item.name] += item.total;
        });
    });

    // Most popular services
    stats.services.mostPopular = Object.entries(stats.services.totalRevenue)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([name, revenue]) => ({ name, revenue }));

    return stats;
}

function generateMonthlyReport(year = new Date().getFullYear()) {
    const monthlyData = Array(12).fill(0).map((_, index) => ({
        month: index + 1,
        monthName: new Date(year, index).toLocaleDateString('sk-SK', { month: 'long' }),
        invoices: 0,
        revenue: 0,
        newClients: 0,
        paidInvoices: 0
    }));

    // Process invoices
    invoices.forEach(invoice => {
        const date = new Date(invoice.issueDate);
        if (date.getFullYear() === year) {
            const monthIndex = date.getMonth();
            monthlyData[monthIndex].invoices++;
            monthlyData[monthIndex].revenue += invoice.total;

            if (invoice.status === 'paid') {
                monthlyData[monthIndex].paidInvoices++;
            }
        }
    });

    // Process clients
    clients.forEach(client => {
        const date = new Date(client.createdAt);
        if (date.getFullYear() === year) {
            const monthIndex = date.getMonth();
            monthlyData[monthIndex].newClients++;
        }
    });

    return monthlyData;
}

function exportReport(type = 'monthly') {
    let data, filename, headers;

    switch(type) {
        case 'monthly':
            data = generateMonthlyReport();
            filename = `mesacny_report_${new Date().getFullYear()}.csv`;
            headers = ['Mesiac', 'Faktúry', 'Tržby (EUR)', 'Noví klienti', 'Uhradené faktúry'];
            break;

        case 'clients':
            data = clients.map(client => [
                client.name,
                client.phone,
                client.email,
                client.totalRevenue,
                client.activeContracts,
                formatDate(client.createdAt)
            ]);
            filename = `klienti_${new Date().toISOString().split('T')[0]}.csv`;
            headers = ['Meno', 'Telefón', 'Email', 'Tržby', 'Zmluvy', 'Vytvorené'];
            break;

        case 'invoices':
            data = invoices.map(invoice => {
                const client = clients.find(c => c.id === invoice.clientId);
                return [
                    invoice.number,
                    client ? client.name : 'Neznámy',
                    formatDate(invoice.issueDate),
                    formatDate(invoice.dueDate),
                    invoice.total,
                    getInvoiceStatusText(invoice),
                    invoice.paidDate ? formatDate(invoice.paidDate) : ''
                ];
            });
            filename = `faktury_${new Date().toISOString().split('T')[0]}.csv`;
            headers = ['Číslo', 'Klient', 'Vystavené', 'Splatnosť', 'Suma', 'Stav', 'Uhradené'];
            break;

        default:
            alert('Neznámy typ reportu');
            return;
    }

    const csvContent = [
        headers,
        ...data.map(row => Array.isArray(row) ? row : [
            row.monthName || row.month,
            row.invoices || row[1],
            row.revenue || row[2],
            row.newClients || row[3],
            row.paidInvoices || row[4]
        ])
    ].map(row =>
        row.map(field => `"${field}"`).join(',')
    ).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    link.click();

    showNotification(`Report ${filename} bol exportovaný`, 'success');
}

function showAdvancedStatsModal() {
    const stats = generateAdvancedStats();

    // Create modal HTML if it doesn't exist
    let modal = document.getElementById('advancedStatsModal');
    if (!modal) {
        modal = createAdvancedStatsModal();
        document.body.appendChild(modal);
    }

    // Populate modal content
    populateAdvancedStats(stats);

    modal.style.display = 'flex';
}

function createAdvancedStatsModal() {
    const modal = document.createElement('div');
    modal.id = 'advancedStatsModal';
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h3><i class="fas fa-chart-bar"></i> Pokročilé štatistiky</h3>
                <span class="close" onclick="closeModal('advancedStatsModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div id="advancedStatsContent">
                    <!-- Content will be populated by JavaScript -->
                </div>
                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="exportReport('monthly')">
                        <i class="fas fa-download"></i> Export mesačný report
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="exportReport('clients')">
                        <i class="fas fa-download"></i> Export klienti
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="exportReport('invoices')">
                        <i class="fas fa-download"></i> Export faktúry
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('advancedStatsModal')">
                        Zavrieť
                    </button>
                </div>
            </div>
        </div>
    `;

    return modal;
}

function populateAdvancedStats(stats) {
    const content = document.getElementById('advancedStatsContent');
    if (!content) return;

    content.innerHTML = `
        <div class="advanced-stats-layout">
            <div class="stats-section">
                <h4><i class="fas fa-users"></i> Klienti</h4>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-label">Celkom klientov:</span>
                        <span class="stat-value">${stats.clients.total}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Noví tento mesiac:</span>
                        <span class="stat-value">${stats.clients.newThisMonth}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Priemerné tržby:</span>
                        <span class="stat-value">${formatCurrency(stats.clients.averageRevenue)}</span>
                    </div>
                </div>

                <h5>Top 5 klientov</h5>
                <div class="top-clients-list">
                    ${stats.clients.topClients.map(client => `
                        <div class="top-client-item">
                            <span class="client-name">${client.name}</span>
                            <span class="client-revenue">${formatCurrency(client.revenue)}</span>
                        </div>
                    `).join('')}
                </div>
            </div>

            <div class="stats-section">
                <h4><i class="fas fa-file-invoice"></i> Faktúry</h4>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-label">Celkom faktúr:</span>
                        <span class="stat-value">${stats.invoices.total}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Uhradené:</span>
                        <span class="stat-value">${stats.invoices.paid} (${formatCurrency(stats.invoices.paidAmount)})</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Čakajúce:</span>
                        <span class="stat-value">${stats.invoices.pending} (${formatCurrency(stats.invoices.pendingAmount)})</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Po splatnosti:</span>
                        <span class="stat-value">${stats.invoices.overdue} (${formatCurrency(stats.invoices.overdueAmount)})</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Tento mesiac:</span>
                        <span class="stat-value">${stats.invoices.thisMonth} (${formatCurrency(stats.invoices.thisMonthAmount)})</span>
                    </div>
                </div>
            </div>

            <div class="stats-section">
                <h4><i class="fas fa-cogs"></i> Najobľúbenejšie služby</h4>
                <div class="popular-services-list">
                    ${stats.services.mostPopular.map((service, index) => `
                        <div class="popular-service-item">
                            <span class="service-rank">${index + 1}.</span>
                            <span class="service-name">${service.name}</span>
                            <span class="service-revenue">${formatCurrency(service.revenue)}</span>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
    `;
}

// Export new functions
window.generateAdvancedStats = generateAdvancedStats;
window.generateMonthlyReport = generateMonthlyReport;
window.exportReport = exportReport;
window.showAdvancedStatsModal = showAdvancedStatsModal;

// System maintenance functions
function clearAllData() {
    if (!confirm('POZOR: Táto akcia vymaže všetky dáta (klientov, faktúry, objednávky). Táto akcia sa nedá vrátiť späť!\n\nNaozaj chcete pokračovať?')) {
        return;
    }

    if (!confirm('Posledné upozornenie: Všetky dáta budú natrvalo vymazané. Ste si istí?')) {
        return;
    }

    // Clear all localStorage data
    localStorage.removeItem('crm_clients');
    localStorage.removeItem('crm_invoices');
    localStorage.removeItem('crm_invoice_counter');
    localStorage.removeItem('crm_services');
    localStorage.removeItem('orders');
    localStorage.removeItem('tasks');
    localStorage.removeItem('orderCounter');
    localStorage.removeItem('taskCounter');

    // Reset global variables
    clients.length = 0;
    invoices.length = 0;
    invoiceCounter = 1;

    if (typeof orders !== 'undefined') {
        orders.length = 0;
    }
    if (typeof tasks !== 'undefined') {
        tasks.length = 0;
    }

    // Update displays
    updateClientsDisplay();
    updateInvoicesDisplay();
    updateServicesDisplay();
    updateCRMStats();

    if (typeof renderTasks === 'function') {
        renderTasks();
    }

    showNotification('Všetky dáta boli vymazané', 'success');
}

function exportAllData() {
    const allData = {
        clients: clients,
        invoices: invoices,
        invoiceCounter: invoiceCounter,
        services: SERVICES,
        orders: typeof orders !== 'undefined' ? orders : [],
        tasks: typeof tasks !== 'undefined' ? tasks : [],
        exportDate: new Date().toISOString(),
        version: '1.0'
    };

    const dataStr = JSON.stringify(allData, null, 2);
    const blob = new Blob([dataStr], { type: 'application/json' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `espomienka_backup_${new Date().toISOString().split('T')[0]}.json`;
    link.click();

    showNotification('Záloha dát bola vytvorená', 'success');
}

function importData() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';

    input.onchange = function(event) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const data = JSON.parse(e.target.result);

                if (!data.version || !data.exportDate) {
                    alert('Neplatný formát zálohy');
                    return;
                }

                if (!confirm(`Obnoviť dáta zo zálohy z ${formatDate(data.exportDate)}?\n\nTáto akcia prepíše všetky existujúce dáta.`)) {
                    return;
                }

                // Restore data
                if (data.clients) {
                    clients.length = 0;
                    clients.push(...data.clients.map(clientData => new Client(clientData)));
                    saveClientsData();
                }

                if (data.invoices) {
                    invoices.length = 0;
                    invoices.push(...data.invoices.map(invoiceData => new Invoice(invoiceData)));
                    saveInvoicesData();
                }

                if (data.invoiceCounter) {
                    invoiceCounter = data.invoiceCounter;
                    localStorage.setItem('crm_invoice_counter', invoiceCounter.toString());
                }

                if (data.services) {
                    Object.assign(SERVICES, data.services);
                    localStorage.setItem('crm_services', JSON.stringify(SERVICES));
                }

                if (data.orders && typeof orders !== 'undefined') {
                    orders.length = 0;
                    orders.push(...data.orders);
                    if (typeof saveOrdersData === 'function') {
                        saveOrdersData();
                    }
                }

                if (data.tasks && typeof tasks !== 'undefined') {
                    tasks.length = 0;
                    tasks.push(...data.tasks);
                    if (typeof saveOrdersData === 'function') {
                        saveOrdersData();
                    }
                }

                // Update displays
                updateClientsDisplay();
                updateInvoicesDisplay();
                updateServicesDisplay();
                updateCRMStats();

                if (typeof renderTasks === 'function') {
                    renderTasks();
                }

                showNotification('Dáta boli úspešne obnovené', 'success');

            } catch (error) {
                alert('Chyba pri načítavaní zálohy: ' + error.message);
            }
        };

        reader.readAsText(file);
    };

    input.click();
}

// Settings management
function saveSettings() {
    const settings = {
        defaultLocation: document.getElementById('defaultLocation')?.value,
        autoScheduling: document.getElementById('autoScheduling')?.checked,
        autoInvoiceNumbering: document.getElementById('autoInvoiceNumbering')?.checked,
        defaultDueDays: parseInt(document.getElementById('defaultDueDays')?.value) || 2,
        vatRate: parseFloat(document.getElementById('vatRate')?.value) || 20,
        companyPhone: document.getElementById('companyPhone')?.value,
        companyEmail: document.getElementById('companyEmail')?.value,
        companyWebsite: document.getElementById('companyWebsite')?.value
    };

    localStorage.setItem('crm_settings', JSON.stringify(settings));

    // Update company data
    if (settings.companyPhone) COMPANY_DATA.phone = settings.companyPhone;
    if (settings.companyEmail) COMPANY_DATA.email = settings.companyEmail;
    if (settings.companyWebsite) COMPANY_DATA.website = settings.companyWebsite;

    showNotification('Nastavenia boli uložené', 'success');
}

function loadSettings() {
    const savedSettings = localStorage.getItem('crm_settings');
    if (!savedSettings) return;

    try {
        const settings = JSON.parse(savedSettings);

        if (document.getElementById('defaultLocation')) {
            document.getElementById('defaultLocation').value = settings.defaultLocation || 'bratislava';
        }
        if (document.getElementById('autoScheduling')) {
            document.getElementById('autoScheduling').checked = settings.autoScheduling !== false;
        }
        if (document.getElementById('autoInvoiceNumbering')) {
            document.getElementById('autoInvoiceNumbering').checked = settings.autoInvoiceNumbering !== false;
        }
        if (document.getElementById('defaultDueDays')) {
            document.getElementById('defaultDueDays').value = settings.defaultDueDays || 2;
        }
        if (document.getElementById('vatRate')) {
            document.getElementById('vatRate').value = settings.vatRate || 20;
        }
        if (document.getElementById('companyPhone')) {
            document.getElementById('companyPhone').value = settings.companyPhone || COMPANY_DATA.phone;
        }
        if (document.getElementById('companyEmail')) {
            document.getElementById('companyEmail').value = settings.companyEmail || COMPANY_DATA.email;
        }
        if (document.getElementById('companyWebsite')) {
            document.getElementById('companyWebsite').value = settings.companyWebsite || COMPANY_DATA.website;
        }

        // Update company data
        if (settings.companyPhone) COMPANY_DATA.phone = settings.companyPhone;
        if (settings.companyEmail) COMPANY_DATA.email = settings.companyEmail;
        if (settings.companyWebsite) COMPANY_DATA.website = settings.companyWebsite;

    } catch (error) {
        console.error('Error loading settings:', error);
    }
}

// Initialize settings on page load
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(loadSettings, 500);

    // Add event listeners for settings
    const settingsInputs = ['defaultLocation', 'autoScheduling', 'autoInvoiceNumbering',
                           'defaultDueDays', 'vatRate', 'companyPhone', 'companyEmail', 'companyWebsite'];

    settingsInputs.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', saveSettings);
        }
    });
});

// Export maintenance functions
window.clearAllData = clearAllData;
window.exportAllData = exportAllData;
window.importData = importData;
window.saveSettings = saveSettings;
window.loadSettings = loadSettings;

// Testing functions
function testPDFGeneration() {
    // Create test invoice data
    const testClient = {
        id: 'test-client',
        name: 'Test Klient',
        phone: '+*********** 456',
        email: '<EMAIL>',
        address: 'Testovacia 123, 12345 Testovo',
        ico: '12345678',
        dic: '1234567890'
    };

    const testInvoice = {
        id: 'test-invoice',
        number: '2025999',
        clientId: 'test-client',
        issueDate: new Date().toISOString().split('T')[0],
        dueDate: calculateDueDate(2),
        items: [
            {
                serviceId: 'sviatocny_jednohrob',
                name: 'Balík Sviatočný - Jednohrob',
                price: 104,
                quantity: 1,
                total: 104
            }
        ],
        subtotal: 86.67,
        vat: 17.33,
        total: 104,
        status: 'pending',
        notes: 'Toto je testovacia faktúra pre overenie PDF generovania.',
        variableSymbol: '2025999'
    };

    console.log('Testing PDF generation...');
    console.log('html2pdf available:', typeof window.html2pdf !== 'undefined');

    if (typeof window.html2pdf !== 'undefined') {
        showNotification('PDF generátor je dostupný. Generujem test PDF...', 'success');
        generateInvoicePDF('test-invoice');

        // Temporarily add test data
        const originalInvoices = [...invoices];
        const originalClients = [...clients];

        invoices.push(testInvoice);
        clients.push(testClient);

        setTimeout(() => {
            generateInvoicePDF('test-invoice');

            // Restore original data
            invoices.length = 0;
            invoices.push(...originalInvoices);
            clients.length = 0;
            clients.push(...originalClients);
        }, 100);

    } else {
        showNotification('PDF generátor nie je dostupný. Skúšam záložnú metódu...', 'warning');
        generateInvoicePDFPrint(testInvoice, testClient);
    }
}

function createSampleData() {
    if (!confirm('Vytvoriť vzorové dáta? Toto pridá testovacích klientov a faktúry.')) {
        return;
    }

    // Sample clients
    const sampleClients = [
        {
            name: 'Anna Nováková',
            phone: '+*********** 456',
            email: '<EMAIL>',
            address: 'Hlavná 15, 811 01 Bratislava',
            totalRevenue: 450,
            activeContracts: 1
        },
        {
            name: 'Boris Kováč',
            phone: '+421 902 654 321',
            email: '<EMAIL>',
            address: 'Košická 25, 040 01 Košice',
            totalRevenue: 180,
            activeContracts: 1
        },
        {
            name: 'Mária Svoboda',
            phone: '+421 903 789 012',
            email: '<EMAIL>',
            address: 'Trenčianska 8, 911 01 Trenčín',
            totalRevenue: 58,
            activeContracts: 1
        }
    ];

    // Add sample clients
    sampleClients.forEach(clientData => {
        const client = new Client(clientData);
        clients.push(client);

        // Create sample invoice for each client
        const invoiceData = {
            clientId: client.id,
            items: [
                new InvoiceItem({
                    serviceId: 'sviatocny_jednohrob',
                    name: 'Balík Sviatočný - Jednohrob',
                    price: 104,
                    quantity: 1,
                    total: 104
                })
            ],
            subtotal: 86.67,
            vat: 17.33,
            total: 104,
            status: Math.random() > 0.5 ? 'paid' : 'pending',
            notes: 'Vzorová faktúra vytvorená pre testovanie'
        };

        const invoice = new Invoice(invoiceData);
        invoices.push(invoice);
    });

    // Save data
    saveClientsData();
    saveInvoicesData();

    // Update displays
    updateClientsDisplay();
    updateInvoicesDisplay();
    updateCRMStats();

    showNotification(`Vytvorených ${sampleClients.length} vzorových klientov a faktúr`, 'success');
}

// Export test functions
window.testPDFGeneration = testPDFGeneration;
window.createSampleData = createSampleData;
